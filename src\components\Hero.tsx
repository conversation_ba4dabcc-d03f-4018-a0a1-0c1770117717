import React from 'react';
import { ChevronDown, Code, Palette, Video, FileText, Monitor } from 'lucide-react';

const Hero: React.FC = () => {
  const scrollToServices = () => {
    const element = document.getElementById('services');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const floatingTags = [
    { icon: Code, label: 'Web Dev', delay: '0s' },
    { icon: Palette, label: 'UI/UX', delay: '0.2s' },
    { icon: Video, label: 'Video', delay: '0.4s' },
    { icon: FileText, label: 'Content', delay: '0.6s' },
    { icon: Monitor, label: 'Graphics', delay: '0.8s' }
  ];

  return (
    <section id="home" className="min-h-screen bg-gradient-to-br from-gray-50 to-white flex items-center pt-20">
      <div className="max-w-7xl mx-auto px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-5 gap-12 items-center">
          {/* Left Content - 60% */}
          <div className="lg:col-span-3 space-y-8 animate-fade-in">
            <div className="space-y-6">
              <h1 className="text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 leading-tight">
                We Build{' '}
                <span className="bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent">
                  Digital Experiences
                </span>{' '}
                That Convert
              </h1>
              <p className="text-xl text-gray-600 max-w-2xl leading-relaxed">
                Premium creative technology solutions for startups and enterprises. 
                Transform your vision into powerful digital experiences that drive results.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <button 
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-gradient-to-r from-blue-600 to-cyan-400 text-white px-8 py-4 rounded-full text-lg font-medium hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Start Your Project
              </button>
              <button 
                onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
                className="border-2 border-gray-300 text-gray-700 px-8 py-4 rounded-full text-lg font-medium hover:border-blue-600 hover:text-blue-600 transition-all duration-300"
              >
                View Our Work
              </button>
            </div>
          </div>

          {/* Right Visual - 40% */}
          <div className="lg:col-span-2 relative">
            <div className="relative h-96 lg:h-[500px] bg-gradient-to-br from-blue-100 to-cyan-50 rounded-3xl overflow-hidden shadow-2xl">
              <img 
                src="https://images.pexels.com/photos/3184463/pexels-photo-3184463.jpeg?auto=compress&cs=tinysrgb&w=800" 
                alt="Creative Technology" 
                className="w-full h-full object-cover"
              />
              
              {/* Floating Service Tags */}
              <div className="absolute inset-0 p-8">
                {floatingTags.map((tag, index) => (
                  <div
                    key={index}
                    className="absolute bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full shadow-lg flex items-center space-x-2 animate-float"
                    style={{
                      animationDelay: tag.delay,
                      top: `${20 + (index * 15)}%`,
                      left: `${10 + (index % 2) * 60}%`,
                    }}
                  >
                    <tag.icon size={16} className="text-blue-600" />
                    <span className="text-sm font-medium text-gray-800">{tag.label}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="flex justify-center mt-16">
          <button
            onClick={scrollToServices}
            className="animate-bounce p-2 rounded-full hover:bg-gray-100 transition-colors duration-300"
          >
            <ChevronDown size={24} className="text-gray-600" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;