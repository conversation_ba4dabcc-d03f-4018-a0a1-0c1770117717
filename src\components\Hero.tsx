import React from 'react';
import { ChevronDown, Code, Palette, Video, FileText, Monitor } from 'lucide-react';

const Hero: React.FC = () => {
  const scrollToServices = () => {
    const element = document.getElementById('services');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const floatingTags = [
    { icon: Code, label: 'Web Dev', delay: '0s' },
    { icon: Palette, label: 'UI/UX', delay: '0.2s' },
    { icon: Video, label: 'Video', delay: '0.4s' },
    { icon: FileText, label: 'Content', delay: '0.6s' },
    { icon: Monitor, label: 'Graphics', delay: '0.8s' }
  ];

  return (
    <section id="home" className="min-h-screen relative flex items-center pt-20 overflow-hidden">
      {/* Blue gradient background with diagonal design */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-blue-700 to-purple-800"></div>

      {/* Diagonal design element */}
      <div className="absolute inset-0">
        <svg
          className="absolute bottom-0 right-0 w-full h-full"
          viewBox="0 0 1200 800"
          preserveAspectRatio="none"
        >
          <path
            d="M1200,800 L1200,200 Q800,600 400,400 Q200,300 0,500 L0,800 Z"
            fill="white"
            opacity="1"
          />
        </svg>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 lg:px-8 py-20">
        <div className="grid lg:grid-cols-5 gap-12 items-center">
          {/* Left Content - 60% */}
          <div className="lg:col-span-3 space-y-8 animate-fade-in">
            {/* Small tag */}
            <div className="inline-block">
              <span className="bg-orange-400 text-white px-4 py-2 rounded-full text-sm font-medium">
                We Are Product Designer From UK
              </span>
            </div>

            <div className="space-y-6">
              <h1 className="text-5xl lg:text-6xl xl:text-7xl font-bold text-white leading-tight">
                We Design Interfaces{' '}
                <span className="block">
                  That Users Love
                </span>
              </h1>
              <p className="text-xl text-blue-100 max-w-2xl leading-relaxed">
                Morbi sed lacus nec risus finibus feugiat et fermentum nibh. Pellentesque
                vitae ante et elit fringilla ac et purus.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="button"
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-4 rounded-full text-lg font-medium hover:shadow-xl transform hover:scale-105 transition-all duration-300"
              >
                Learn More
              </button>
            </div>
          </div>

          {/* Right Visual - 40% */}
          <div className="lg:col-span-2 relative">
            {/* 3D Isometric Illustration Container */}
            <div className="relative h-96 lg:h-[500px] flex items-center justify-center">
              {/* Isometric workspace illustration */}
              <div className="relative w-80 h-80 lg:w-96 lg:h-96">
                {/* Desk/Platform */}
                <div className="absolute bottom-0 left-1/2 w-72 h-32 bg-gradient-to-br from-orange-200 to-orange-300 rounded-lg shadow-lg isometric-desk">
                </div>

                {/* Computer/Screen */}
                <div className="absolute bottom-16 left-1/2 w-32 h-24 bg-gradient-to-br from-pink-400 to-pink-500 rounded-lg shadow-xl isometric-screen">
                  {/* Screen content lines */}
                  <div className="absolute top-2 left-2 right-2 space-y-1">
                    <div className="h-1 bg-white rounded-full opacity-80"></div>
                    <div className="h-1 bg-white rounded-full opacity-60 w-3/4"></div>
                    <div className="h-1 bg-white rounded-full opacity-40 w-1/2"></div>
                  </div>
                  {/* Download icon */}
                  <div className="absolute top-2 right-2 w-3 h-3 bg-white rounded-full opacity-80"></div>
                </div>

                {/* Character */}
                <div className="absolute bottom-20 left-1/2 transform translate-x-4">
                  {/* Body */}
                  <div className="w-8 h-12 bg-gradient-to-b from-purple-400 to-purple-500 rounded-t-full"></div>
                  {/* Head */}
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-6 h-6 bg-orange-300 rounded-full"></div>
                  {/* Hair */}
                  <div className="absolute -top-5 left-1/2 transform -translate-x-1/2 w-7 h-4 bg-orange-600 rounded-t-full"></div>
                </div>

                {/* Lamp */}
                <div className="absolute bottom-24 left-8">
                  {/* Lamp base */}
                  <div className="w-3 h-8 bg-orange-400 rounded-full"></div>
                  {/* Lamp shade */}
                  <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-6 h-4 bg-orange-500 rounded-t-full"></div>
                </div>

                {/* Pencil holder */}
                <div className="absolute bottom-16 right-8 w-6 h-8 bg-gradient-to-b from-yellow-300 to-yellow-400 rounded-lg">
                  {/* Pencils */}
                  <div className="absolute -top-2 left-1 w-1 h-6 bg-green-400 rounded-full"></div>
                  <div className="absolute -top-3 left-3 w-1 h-7 bg-blue-400 rounded-full"></div>
                  <div className="absolute -top-1 right-1 w-1 h-5 bg-red-400 rounded-full"></div>
                </div>

                {/* Boxes */}
                <div className="absolute bottom-12 left-16 w-8 h-6 bg-gradient-to-br from-orange-300 to-orange-400 rounded shadow-lg isometric-box">
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="flex justify-center mt-16">
          <button
            type="button"
            onClick={scrollToServices}
            className="animate-bounce p-2 rounded-full hover:bg-white/20 transition-colors duration-300"
            aria-label="Scroll to services section"
          >
            <ChevronDown size={24} className="text-white" />
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;