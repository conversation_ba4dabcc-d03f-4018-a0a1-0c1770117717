import React from 'react';
import { Zap, Clock, Award, Users, Target, Lightbulb, Shield, Rocket } from 'lucide-react';

const About: React.FC = () => {
  const values = [
    {
      icon: Lightbulb,
      title: 'Innovation',
      description: 'We stay ahead of technology trends to deliver cutting-edge solutions'
    },
    {
      icon: Zap,
      title: 'Speed',
      description: 'Fast delivery without compromising on quality or attention to detail'
    },
    {
      icon: Award,
      title: 'Design Excellence',
      description: 'Every pixel matters - we craft beautiful, functional designs'
    },
    {
      icon: Users,
      title: 'Client Success',
      description: 'Your success is our success - we build lasting partnerships'
    }
  ];

  const stats = [
    { number: '200+', label: 'Projects Completed' },
    { number: '50+', label: 'Happy Clients' },
    { number: '5+', label: 'Years Experience' },
    { number: '24/7', label: 'Support Available' }
  ];

  return (
    <section id="about" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            About <span className="bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent">DIGITALX</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We're a team of passionate creators, developers, and strategists dedicated to transforming your digital vision into reality
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Company Story */}
          <div className="space-y-6">
            <h3 className="text-3xl font-bold text-gray-900">Our Story</h3>
            <div className="space-y-4 text-gray-600 leading-relaxed">
              <p>
                Founded with a vision to bridge the gap between creativity and technology, DIGITALX has been at the forefront of digital innovation since our inception. We believe that exceptional digital experiences are born from the perfect harmony of design, technology, and strategy.
              </p>
              <p>
                Our team combines years of industry expertise with a fresh perspective on emerging technologies. We don't just build digital solutions; we craft experiences that resonate with your audience and drive meaningful results for your business.
              </p>
              <p>
                From startups looking to make their mark to established enterprises seeking digital transformation, we've partnered with businesses across industries to turn their vision into reality.
              </p>
            </div>

            {/* Mission Statement */}
            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-2xl">
              <h4 className="text-xl font-bold text-gray-900 mb-3">Our Mission</h4>
              <p className="text-gray-700 italic">
                "To empower businesses with innovative digital solutions that not only meet today's needs but anticipate tomorrow's opportunities."
              </p>
            </div>
          </div>

          {/* Visual/Stats */}
          <div className="space-y-8">
            <div className="relative">
              <img
                src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Team collaboration"
                className="w-full h-80 object-cover rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              {stats.map((stat, index) => (
                <div
                  key={index}
                  className="bg-white p-6 rounded-2xl shadow-lg text-center border border-gray-100 hover:shadow-xl transition-shadow duration-300"
                >
                  <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-600 text-sm">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Core Values */}
        <div className="mb-16">
          <h3 className="text-3xl font-bold text-gray-900 text-center mb-12">Our Core Values</h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div
                key={index}
                className="text-center group"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-cyan-50 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <value.icon size={28} className="text-blue-600" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-3">{value.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">Ready to Work Together?</h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Let's discuss how we can help transform your digital presence and achieve your business goals.
          </p>
          <button
            onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
            className="bg-gradient-to-r from-blue-600 to-cyan-400 text-white px-8 py-4 rounded-full text-lg font-medium hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            Start a Conversation
          </button>
        </div>
      </div>
    </section>
  );
};

export default About;