import React, { useState } from 'react';
import { X, ExternalLink, Github } from 'lucide-react';

const Portfolio: React.FC = () => {
  const [selectedProject, setSelectedProject] = useState<number | null>(null);
  const [filter, setFilter] = useState('All');

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      category: 'Web Development',
      description: 'Modern e-commerce solution with advanced filtering and payment integration.',
      image: 'https://images.pexels.com/photos/3184296/pexels-photo-3184296.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],
      fullDescription: 'A comprehensive e-commerce platform featuring advanced product filtering, secure payment processing, inventory management, and real-time analytics dashboard.',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 2,
      title: 'Healthcare App Design',
      category: 'UI/UX Design',
      description: 'Intuitive healthcare application focusing on patient experience and accessibility.',
      image: 'https://images.pexels.com/photos/4386464/pexels-photo-4386464.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Figma', 'Prototyping', 'User Testing'],
      fullDescription: 'Complete UI/UX design for a healthcare application that prioritizes accessibility and user experience, with comprehensive user research and testing.',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 3,
      title: 'Brand Identity Suite',
      category: 'Graphic Design',
      description: 'Complete brand identity including logo, guidelines, and marketing materials.',
      image: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Adobe Creative Suite', 'Brand Strategy'],
      fullDescription: 'Comprehensive brand identity development including logo design, color palette, typography, and brand guidelines for consistent application across all touchpoints.',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 4,
      title: 'Product Launch Video',
      category: 'Video Editing',
      description: 'High-impact product launch video with motion graphics and professional editing.',
      image: 'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['After Effects', 'Premiere Pro', 'Motion Graphics'],
      fullDescription: 'Professional product launch video featuring custom motion graphics, color correction, and sound design to create maximum impact for product introduction.',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 5,
      title: 'SaaS Dashboard',
      category: 'Web Development',
      description: 'Complex dashboard application with real-time data visualization and analytics.',
      image: 'https://images.pexels.com/photos/3184460/pexels-photo-3184460.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Vue.js', 'D3.js', 'WebSockets', 'Python'],
      fullDescription: 'Advanced SaaS dashboard featuring real-time data visualization, custom charts, user management, and comprehensive analytics with role-based access control.',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 6,
      title: 'Content Marketing Strategy',
      category: 'Content Scripting',
      description: 'Comprehensive content strategy and copywriting for B2B SaaS company.',
      image: 'https://images.pexels.com/photos/3184287/pexels-photo-3184287.jpeg?auto=compress&cs=tinysrgb&w=800',
      technologies: ['Content Strategy', 'SEO Writing', 'Social Media'],
      fullDescription: 'Complete content marketing strategy including blog posts, social media content, email campaigns, and landing page copy that increased conversion rates by 150%.',
      liveUrl: '#',
      githubUrl: '#'
    }
  ];

  const categories = ['All', 'Web Development', 'UI/UX Design', 'Graphic Design', 'Video Editing', 'Content Scripting'];

  const filteredProjects = filter === 'All' 
    ? projects 
    : projects.filter(project => project.category === filter);

  return (
    <section id="portfolio" className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Our <span className="bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent">Portfolio</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Explore our latest projects and see how we've helped businesses transform their digital presence
          </p>

          {/* Filter Buttons */}
          <div className="flex flex-wrap justify-center gap-3">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setFilter(category)}
                className={`px-6 py-2 rounded-full font-medium transition-all duration-300 ${
                  filter === category
                    ? 'bg-gradient-to-r from-blue-600 to-cyan-400 text-white shadow-lg'
                    : 'bg-white text-gray-600 hover:text-blue-600 border border-gray-200 hover:border-blue-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Portfolio Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <div
              key={project.id}
              className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transform hover:-translate-y-2 transition-all duration-500 cursor-pointer"
              style={{ animationDelay: `${index * 0.1}s` }}
              onClick={() => setSelectedProject(project.id)}
            >
              {/* Project Image */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex flex-wrap gap-2 mb-2">
                      {project.technologies.slice(0, 3).map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="px-2 py-1 bg-white/20 backdrop-blur-sm text-white text-xs rounded-full"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Project Content */}
              <div className="p-6">
                <div className="mb-2">
                  <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                    {project.category}
                  </span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                  {project.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {project.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Project Modal */}
        {selectedProject && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="relative">
                {/* Close Button */}
                <button
                  onClick={() => setSelectedProject(null)}
                  className="absolute top-4 right-4 z-10 p-2 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-colors duration-300"
                >
                  <X size={20} />
                </button>

                {/* Project Details */}
                {filteredProjects
                  .filter(p => p.id === selectedProject)
                  .map(project => (
                    <div key={project.id}>
                      <img
                        src={project.image}
                        alt={project.title}
                        className="w-full h-64 md:h-80 object-cover"
                      />
                      
                      <div className="p-8">
                        <div className="mb-4">
                          <span className="text-sm font-medium text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                            {project.category}
                          </span>
                        </div>
                        
                        <h3 className="text-3xl font-bold text-gray-900 mb-4">{project.title}</h3>
                        <p className="text-gray-600 text-lg leading-relaxed mb-6">
                          {project.fullDescription}
                        </p>

                        <div className="mb-6">
                          <h4 className="text-lg font-semibold text-gray-900 mb-3">Technologies Used</h4>
                          <div className="flex flex-wrap gap-2">
                            {project.technologies.map((tech, index) => (
                              <span
                                key={index}
                                className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                              >
                                {tech}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="flex gap-4">
                          <a
                            href={project.liveUrl}
                            className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-cyan-400 text-white px-6 py-3 rounded-full hover:shadow-lg transition-all duration-300"
                          >
                            <ExternalLink size={16} />
                            <span>View Live</span>
                          </a>
                          <a
                            href={project.githubUrl}
                            className="flex items-center space-x-2 border border-gray-300 text-gray-700 px-6 py-3 rounded-full hover:border-blue-600 hover:text-blue-600 transition-all duration-300"
                          >
                            <Github size={16} />
                            <span>View Code</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default Portfolio;