@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(2deg);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Smooth scrolling for the entire document */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #0061FF, #60EFFF);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #0052CC, #4DD0E1);
}

/* Custom focus states for accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  outline: 2px solid #0061FF;
  outline-offset: 2px;
}

/* Loading animation for buttons */
.button-loading {
  position: relative;
}

.button-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Gradient text selection */
::selection {
  background: rgba(0, 97, 255, 0.2);
  color: #0D1B2A;
}

::-moz-selection {
  background: rgba(0, 97, 255, 0.2);
  color: #0D1B2A;
}

/* Custom styles for form elements */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: linear-gradient(to right, #0061FF, #60EFFF);
  outline: none;
  border-radius: 3px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #0061FF;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: white;
  border: 2px solid #0061FF;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Glassmorphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive typography */
@media (max-width: 640px) {
  h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }
  
  h2 {
    font-size: 2rem;
    line-height: 1.3;
  }
  
  .text-hero {
    font-size: 2.25rem;
  }
}

/* Print styles */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .text-gray-600 {
    color: #374151 !important;
  }
  
  .text-gray-500 {
    color: #6B7280 !important;
  }
  
  .border-gray-200 {
    border-color: #9CA3AF !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .animate-float,
  .animate-fade-in {
    animation: none !important;
  }
}

/* 3D Isometric transforms for hero illustration */
.isometric-desk {
  transform: translateX(-50%) perspective(400px) rotateX(60deg) rotateY(-15deg);
}

.isometric-screen {
  transform: translateX(-50%) perspective(400px) rotateX(15deg) rotateY(-10deg);
}

.isometric-box {
  transform: perspective(400px) rotateX(45deg) rotateY(-20deg);
}