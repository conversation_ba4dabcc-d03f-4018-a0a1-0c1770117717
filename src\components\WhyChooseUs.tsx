import React from 'react';
import { Zap, Setting<PERSON>, Heart, Lightbulb } from 'lucide-react';

const WhyChooseUs: React.FC = () => {
  const benefits = [
    {
      icon: Zap,
      title: 'Lightning Fast Delivery',
      description: 'Get your projects completed in record time without compromising quality',
      gradient: 'from-yellow-400 to-orange-500'
    },
    {
      icon: Settings,
      title: 'Cutting-Edge Tools',
      description: 'We use the latest technologies and frameworks for optimal performance',
      gradient: 'from-purple-400 to-pink-500'
    },
    {
      icon: Heart,
      title: 'Client-First Approach',
      description: 'Your success is our priority with dedicated support and communication',
      gradient: 'from-red-400 to-pink-500'
    },
    {
      icon: Lightbulb,
      title: 'Creative Innovation',
      description: 'Unique solutions tailored to your brand and business objectives',
      gradient: 'from-blue-400 to-cyan-500'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Why Choose <span className="bg-gradient-to-r from-blue-600 to-cyan-400 bg-clip-text text-transparent">Us</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We combine expertise, innovation, and dedication to deliver exceptional results that exceed expectations
          </p>
        </div>

        {/* Benefits Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transform hover:-translate-y-2 transition-all duration-500"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Icon with Gradient Background */}
              <div className="relative mb-6">
                <div className={`w-16 h-16 bg-gradient-to-r ${benefit.gradient} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <benefit.icon size={28} className="text-white" />
                </div>
                {/* Glow Effect */}
                <div className={`absolute inset-0 w-16 h-16 bg-gradient-to-r ${benefit.gradient} rounded-2xl blur-xl opacity-20 group-hover:opacity-40 transition-opacity duration-300`}></div>
              </div>

              {/* Content */}
              <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                {benefit.title}
              </h3>
              <p className="text-gray-600 leading-relaxed text-sm">
                {benefit.description}
              </p>

              {/* Hover Animation Line */}
              <div className="mt-4 h-1 bg-gradient-to-r from-blue-600 to-cyan-400 rounded-full scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>
            </div>
          ))}
        </div>

        {/* Additional Benefits Section */}
        <div className="mt-20 bg-gradient-to-r from-blue-600 to-cyan-400 rounded-3xl p-12 text-white text-center">
          <h3 className="text-3xl font-bold mb-6">More Than Just a Service Provider</h3>
          <div className="grid md:grid-cols-3 gap-8 mb-8">
            <div>
              <div className="text-4xl font-bold mb-2">48hr</div>
              <div className="text-blue-100">Response Time</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">100%</div>
              <div className="text-blue-100">Project Success Rate</div>
            </div>
            <div>
              <div className="text-4xl font-bold mb-2">∞</div>
              <div className="text-blue-100">Ongoing Support</div>
            </div>
          </div>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-8">
            We don't just deliver projects; we build lasting partnerships that grow with your business
          </p>
          <button
            onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
            className="bg-white text-blue-600 px-8 py-4 rounded-full text-lg font-medium hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            Experience the Difference
          </button>
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;