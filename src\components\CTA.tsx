import React from 'react';
import { ArrowR<PERSON>, Clock, CheckCircle } from 'lucide-react';

const CTA: React.FC = () => {
  return (
    <section className="py-24 bg-gradient-to-br from-blue-600 via-blue-700 to-cyan-600 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent transform -skew-y-3"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full blur-3xl opacity-20"></div>
      </div>

      <div className="max-w-7xl mx-auto px-6 lg:px-8 relative z-10">
        <div className="text-center">
          {/* Main Headline */}
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Ready to Transform Your
            <br />
            <span className="bg-gradient-to-r from-cyan-300 to-white bg-clip-text text-transparent">
              Digital Presence?
            </span>
          </h2>

          {/* Subtitle */}
          <p className="text-xl lg:text-2xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
            Join hundreds of successful businesses who have transformed their digital presence with our innovative solutions
          </p>

          {/* Benefits List */}
          <div className="flex flex-wrap justify-center gap-8 mb-12 text-blue-100">
            <div className="flex items-center space-x-2">
              <CheckCircle size={20} className="text-cyan-300" />
              <span>Free consultation</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock size={20} className="text-cyan-300" />
              <span>48-hour response time</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle size={20} className="text-cyan-300" />
              <span>No long-term contracts</span>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row justify-center gap-4 mb-8">
            <button
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              className="group bg-white text-blue-600 px-8 py-4 rounded-full text-lg font-medium hover:shadow-2xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2"
            >
              <span>Start Your Project Today</span>
              <ArrowRight size={20} className="group-hover:translate-x-1 transition-transform duration-300" />
            </button>
            
            <button
              onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
              className="border-2 border-white/30 text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-white/10 hover:border-white transition-all duration-300"
            >
              View Our Portfolio
            </button>
          </div>

          {/* Secondary Text */}
          <p className="text-blue-200 text-lg">
            🚀 Special offer: <strong>20% off</strong> your first project when you start this month
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-white/20">
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">200+</div>
            <div className="text-blue-200">Projects Delivered</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">98%</div>
            <div className="text-blue-200">Client Satisfaction</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">24/7</div>
            <div className="text-blue-200">Support Available</div>
          </div>
          <div className="text-center">
            <div className="text-4xl font-bold text-white mb-2">5★</div>
            <div className="text-blue-200">Average Rating</div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;